package za.co.wethinkcode.robots.webapi;

import org.junit.jupiter.a.*;
import za.co.wethinkcode.robots.infrastructure.Server;

import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HTTP Integration test that actually tests the Web API endpoints via HTTP requests
 * This is equivalent to testing with curl commands
 */
public class HttpApiIntegrationTest {
    private static final int SOCKET_PORT = 5002;
    private static final int HTTP_PORT = 8082;
    private static final String BASE_URL = "http://localhost:" + HTTP_PORT;
    private static Thread serverThread;

    @BeforeAll
    public static void startServer() throws InterruptedException {
        // Start the integrated server in a separate thread
        serverThread = new Thread(() -> {
            try {
                // Configure ports to avoid conflicts with other tests
                Server.parseArgs(new String[]{"-p", String.valueOf(SOCKET_PORT), "-h", String.valueOf(HTTP_PORT), "-s", "5"});
                Server.startServer(SOCKET_PORT);
            } catch (IOException e) {
                System.err.println("Failed to start server: " + e.getMessage());
            }
        });
        serverThread.setDaemon(true);
        serverThread.start();
        
        // Give the server time to start
        Thread.sleep(3000);
        
        System.out.println("Test server should be running on HTTP port " + HTTP_PORT);
    }

    @AfterAll
    public static void stopServer() {
        Server.shutdown();
        if (serverThread != null) {
            serverThread.interrupt();
        }
    }

    @Test
    @DisplayName("HTTP GET /world - Should return world state (equivalent to: curl -X GET http://localhost:8082/world)")
    public void testGetWorldViaHttp() throws UnirestException {
        HttpResponse<String> response = Unirest.get(BASE_URL + "/world").asString();
        
        // Verify HTTP response
        assertEquals(200, response.getStatus());
        
        String responseBody = response.getBody();
        assertNotNull(responseBody);
        
        // Verify JSON structure (like parsing curl output)
        assertTrue(responseBody.contains("\"width\""), "Response should contain width field");
        assertTrue(responseBody.contains("\"height\""), "Response should contain height field");
        assertTrue(responseBody.contains("\"obstacles\""), "Response should contain obstacles array");
        assertTrue(responseBody.contains("\"robots\""), "Response should contain robots array");
        
        System.out.println("GET /world response: " + responseBody);
    }

    @Test
    @DisplayName("HTTP POST /robot/{name} - Should launch robot (equivalent to: curl -X POST -d '{...}' http://localhost:8082/robot/TestBot)")
    public void testLaunchRobotViaHttp() throws UnirestException {
        String launchCommand = "{\"command\":\"launch\",\"arguments\":[\"tank\"]}";

        HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/HttpTestBot")
                .header("Content-Type", "application/json")
                .body(launchCommand)
                .asString();

        // Verify HTTP response
        assertEquals(201, response.getStatus());
        
        String responseBody = response.getBody();
        assertNotNull(responseBody);
        
        // Verify response structure (like parsing curl output)
        assertTrue(responseBody.contains("\"result\""), "Response should contain result field");
        assertTrue(responseBody.contains("\"data\""), "Response should contain data field");
        assertTrue(responseBody.contains("\"state\""), "Response should contain state field");
        
        System.out.println("POST /robot/HttpTestBot response: " + responseBody);
    }

    @Test
    @DisplayName("HTTP GET /world after robot launch - Should show robot in world state")
    public void testWorldStateAfterRobotLaunch() throws UnirestException {
        // First, launch a robot
        String launchCommand = "{\"command\":\"launch\",\"arguments\":[\"sniper\"]}";
        HttpResponse<String> launchResponse = Unirest.post(BASE_URL + "/robot/VisibilityBot")
                .header("Content-Type", "application/json")
                .body(launchCommand)
                .asString();

        assertEquals(201, launchResponse.getStatus());

        // Then, check world state
        HttpResponse<String> worldResponse = Unirest.get(BASE_URL + "/world").asString();
        assertEquals(200, worldResponse.getStatus());
        
        String worldBody = worldResponse.getBody();
        
        // Verify robot appears in world state
        assertTrue(worldBody.contains("VisibilityBot"), "World should contain the launched robot");
        assertTrue(worldBody.contains("sniper"), "World should show robot type");
        
        System.out.println("World state after launch: " + worldBody);
    }

    @Test
    @DisplayName("HTTP POST with invalid command - Should return 400 (equivalent to: curl with invalid JSON)")
    public void testInvalidCommandViaHttp() throws UnirestException {
        String invalidCommand = "{\"command\":\"invalid\",\"arguments\":[]}";

        HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/InvalidBot")
                .header("Content-Type", "application/json")
                .body(invalidCommand)
                .asString();

        // Should return Bad Request
        assertEquals(400, response.getStatus());
        
        String responseBody = response.getBody();
        assertTrue(responseBody.contains("launch"), "Error message should mention supported commands");
        
        System.out.println("Invalid command response (400): " + responseBody);
    }

    @Test
    @DisplayName("HTTP POST with malformed JSON - Should return 400")
    public void testMalformedJsonViaHttp() throws UnirestException {
        String malformedJson = "{\"command\":\"launch\"}"; // Missing arguments

        HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/MalformedBot")
                .header("Content-Type", "application/json")
                .body(malformedJson)
                .asString();

        // Should return Bad Request
        assertEquals(400, response.getStatus());
        
        System.out.println("Malformed JSON response (400): " + response.getBody());
    }

    @Test
    @DisplayName("Multiple robots via HTTP - Should handle multiple launches")
    public void testMultipleRobotsViaHttp() throws UnirestException {
        // Launch multiple robots
        String[] robotTypes = {"tank", "sniper"};
        String[] robotNames = {"MultiBot1", "MultiBot2"};
        
        for (int i = 0; i < robotTypes.length; i++) {
            String launchCommand = "{\"command\":\"launch\",\"arguments\":[\"" + robotTypes[i] + "\"]}";
            HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/" + robotNames[i])
                    .header("Content-Type", "application/json")
                    .body(launchCommand)
                    .asString();
            
            assertEquals(201, response.getStatus());
        }
        
        // Check world state contains all robots
        HttpResponse<String> worldResponse = Unirest.get(BASE_URL + "/world").asString();
        assertEquals(200, worldResponse.getStatus());
        
        String worldBody = worldResponse.getBody();
        for (String robotName : robotNames) {
            assertTrue(worldBody.contains(robotName), "World should contain " + robotName);
        }
        
        System.out.println("World with multiple robots: " + worldBody);
    }
}
